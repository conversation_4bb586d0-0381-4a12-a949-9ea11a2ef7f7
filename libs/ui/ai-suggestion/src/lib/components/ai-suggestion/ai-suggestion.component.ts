import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
  booleanAttribute,
  forwardRef,
  numberAttribute,
} from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  filter,
  interval,
  map,
  of,
  startWith,
  switchMap,
  take,
} from 'rxjs';

/**
 * Reusable AI suggestion component that provides animated typewriter overlay functionality
 * for form controls. Shows AI suggestions with character-by-character typewriter effect on value changes.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-ai-suggestion--docs Storybook Reference}
 */
@Component({
  selector: 'fin-ai-suggestion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './ai-suggestion.component.html',
  styleUrl: './ai-suggestion.component.scss',
  host: {
    class: 'fin-ai-suggestion',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinAiSuggestionComponent),
      multi: true,
    },
  ],
})
export class FinAiSuggestionComponent
  extends FinControlValueAccessor
  implements OnInit, OnChanges
{
  /**
   * Enables AI suggestion animations. When true, suggestions appear on input changes with typewriter effect.
   * Animation only triggers when BOTH `aiEnabled` is true AND input value changes.
   * @default false
   */
  @Input({ transform: booleanAttribute }) aiEnabled = false;

  /**
   * Speed of the typewriter effect in milliseconds per character.
   * Lower values make the typewriter effect faster.
   * @default 100
   */
  @Input({ transform: numberAttribute }) typewriterSpeed = 100;

  /**
   * Emitted when the AI suggestion typewriter animation completes.
   * Only emits when the typewriter animation completes naturally, not when aiEnabled is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  /** Internal BehaviorSubject to manage aiEnabled state reactively */
  private aiEnabledSubject$$ = new BehaviorSubject<boolean>(false);

  /**
   * Observable that manages the complete animation state including typewriter text and CSS class management.
   * This observable handles all side effects and emits the current typewriter text for template display.
   * Template subscribes to this to display the character-by-character animation and trigger side effects.
   */
  protected animationState$!: Observable<{
    typewriterText: string;
    isActive: boolean;
  }>;

  constructor(
    injector: Injector,
    private elementRef: ElementRef,
    private renderer: Renderer2,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    // Initialize aiEnabled state
    this.aiEnabledSubject$$.next(this.aiEnabled);

    // Set up the animation state observable
    this.setupAnimationState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle aiEnabled changes
    if (changes['aiEnabled']) {
      const newValue = changes['aiEnabled'].currentValue;
      this.aiEnabledSubject$$.next(newValue);
    }
    // Note: typewriterSpeed changes will be picked up automatically in the next animation cycle
  }

  /**
   * Sets up the animation state observable that manages typewriter animation and side effects.
   * This observable is consumed by the template using the async pipe, eliminating manual subscriptions.
   */
  private setupAnimationState(): void {
    this.animationState$ = combineLatest([
      this.aiEnabledSubject$$,
      this.control.valueChanges.pipe(startWith(this.control.value)),
    ]).pipe(
      // Only proceed when aiEnabled is true and we have a value change
      filter(([aiEnabled]: [boolean, string | null]) => aiEnabled === true),
      // Switch to typewriter animation for each new value
      switchMap(
        ([, value]: [boolean, string | null]): Observable<{
          typewriterText: string;
          isActive: boolean;
        }> => {
          // Handle edge cases: empty, null, or undefined values
          const textToType = value?.toString() || '';

          if (textToType.length === 0) {
            // For empty values, return observable with empty text
            return of({ typewriterText: '', isActive: false });
          }

          // Create typewriter effect using interval and map
          return interval(this.typewriterSpeed).pipe(
            take(textToType.length + 1), // +1 for the completion state
            map((index: number) => {
              if (index === 0) {
                // First emission: add CSS class and start animation
                this.renderer.addClass(
                  this.elementRef.nativeElement,
                  'fin-ai-suggestion-active',
                );
                return {
                  typewriterText: textToType.substring(0, 1),
                  isActive: true,
                };
              } else if (index < textToType.length) {
                // Continue typewriter effect
                return {
                  typewriterText: textToType.substring(0, index + 1),
                  isActive: true,
                };
              } else {
                // Final emission: complete animation and cleanup
                this.renderer.removeClass(
                  this.elementRef.nativeElement,
                  'fin-ai-suggestion-active',
                );
                this.aiSuggestionReady.emit();
                return {
                  typewriterText: '',
                  isActive: false,
                };
              }
            }),
          );
        },
      ),
      startWith({ typewriterText: '', isActive: false }),
    );
  }
}
